databaseChangeLog:
  - changeSet:
      id: 1
      author: admin
      changes:
        - createTable:
            tableName: thongketheongay_dongia
            columns:
              - column:
                  name: idthongketheongay_dongia
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: idthongketheongay
                  type: INT
                  constraints:
                    nullable: false
              - column:
                  name: ma_bang_gia
                  type: varchar(10) charset latin1
                  constraints:
                    nullable: false
              - column:
                  name: giaMua
                  type: INT
              - column:
                  name: giaBan
                  type: INT
        - createIndex:
            tableName: thongketheongay_dongia
            columns:
              - column:
                  name: idthongketheongay
            indexName: idx_thongketheongay
        - createIndex:
            tableName: thongketheongay_dongia
            columns:
              - column:
                  name: ma_bang_gia
            indexName: idx_ma_bang_gia
        - addForeignKeyConstraint:
            constraintName: fk_idthongketheongay
            baseTableName: thongketheongay_dongia
            baseColumnNames: idthongketheongay
            referencedTableName: thongketheongay
            referencedColumnNames: idthongketheongay
        - addForeignKeyConstraint:
            constraintName: fk_thonketheongay_dongia_ma_bang_gia
            baseTableName: thongketheongay_dongia
            baseColumnNames: ma_bang_gia
            referencedTableName: bang_gia
            referencedColumnNames: ma_bang_gia

  - changeSet:
      id: 2
      author: admin
      changes:
        - createTable:
            tableName: thongketheothangnam_dongia
            columns:
              - column:
                  name: idthongketheothang_dongia
                  type: BIGINT
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: idthongketheothang
                  type: INT
                  constraints:
                    nullable: false
              - column:
                  name: ma_bang_gia
                  type: varchar(10) charset latin1
                  constraints:
                    nullable: false
              - column:
                  name: giaMua
                  type: INT
              - column:
                  name: giaBan
                  type: INT
        - createIndex:
            tableName: thongketheothangnam_dongia
            columns:
              - column:
                  name: idthongketheothang
            indexName: idx_thongketheongay
        - createIndex:
            tableName: thongketheothangnam_dongia
            columns:
              - column:
                  name: ma_bang_gia
            indexName: idx_ma_bang_gia
        - addForeignKeyConstraint:
            constraintName: fk_idthongketheothang
            baseTableName: thongketheothangnam_dongia
            baseColumnNames: idthongketheothang
            referencedTableName: thongketheothangnam
            referencedColumnNames: idthongketheothangnam
        - addForeignKeyConstraint:
            constraintName: fk_thonketheothangnam_dongia_ma_bang_gia
            baseTableName: thongketheothangnam_dongia
            baseColumnNames: ma_bang_gia
            referencedTableName: bang_gia
            referencedColumnNames: ma_bang_gia

  - changeSet:
      id: 3
      author: admin
      changes:
        - addColumn:
            tableName: hoadoncam # The table you want to modify
            columns:
              - column:
                  name: CCCD
                  type: VARCHAR(255) # Define the type and length
                  constraints:
                    nullable: true # Or false if it's required
              - column:
                  name: diaChi
                  type: VARCHAR(255)
                  constraints:
                    nullable: true
  - changeSet:
      id: 4
      author: admin
      changes:
        - createTable:
            tableName: bank_config
            columns:
              - column:
                  name: id
                  type: bigint
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: account_number
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: bank_name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: branch_name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: account_name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: is_default
                  type: tinyint(1)
                  constraints:
                    nullable: false

  - changeSet:
      id: 5
      author: admin
      changes:
        - createTable:
            tableName: payment_tracking
            columns:
              - column:
                  name: tracking_code
                  type: VARCHAR(255) charset utf8mb4 collate utf8mb4_unicode_ci
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: payment_type
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: amount
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: receive_amount
                  type: BIGINT
                  constraints:
                    nullable: true
              - column:
                  name: bank_config_id
                  type: BIGINT
                  constraints:
                    nullable: true
        - addForeignKeyConstraint:
            constraintName: fk_payment_tracking_bank_config
            baseTableName: payment_tracking
            baseColumnNames: bank_config_id
            referencedTableName: bank_config
            referencedColumnNames: id
  - changeSet:
      id: 6
      author: admin
      changes:
        - addColumn:
            tableName: phieu_thanh_toan_hoa_don_cam # The table you want to modify
            columns:
              - column:
                  name: payment_tracking_code
                  type: VARCHAR(255) charset utf8mb4 collate utf8mb4_unicode_ci
                  constraints:
                    nullable: true # Or false if it's required
        - addForeignKeyConstraint:
            constraintName: fk_phieu_thanh_toan_hoa_don_cam_payment_tracking
            baseTableName: phieu_thanh_toan_hoa_don_cam
            baseColumnNames: payment_tracking_code
            referencedTableName: payment_tracking
            referencedColumnNames: tracking_code
        - addColumn:
            tableName: donhang # The table you want to modify
            columns:
              - column:
                  name: payment_tracking_code
                  type: VARCHAR(255) charset utf8mb4 collate utf8mb4_unicode_ci
                  constraints:
                    nullable: true # Or false if it's required

        - addForeignKeyConstraint:
            constraintName: fk_hoa_don_nu_trang_payment_tracking
            baseTableName: donhang
            baseColumnNames: payment_tracking_code
            referencedTableName: payment_tracking
            referencedColumnNames: tracking_code

        - addColumn:
            tableName: hoadonbankhau # The table you want to modify
            columns:
              - column:
                  name: payment_tracking_code
                  type: VARCHAR(255) charset utf8mb4 collate utf8mb4_unicode_ci
                  constraints:
                    nullable: true # Or false if it's required
        - addForeignKeyConstraint:
            constraintName: fk_hoa_don_ban_khau_payment_tracking
            baseTableName: hoadonbankhau
            baseColumnNames: payment_tracking_code
            referencedTableName: payment_tracking
            referencedColumnNames: tracking_code

  # Include your new changelog file
  - include:
      file: db/changelog/2025/06/18-01-changelog.yaml
      relativeToChangelogFile: false
