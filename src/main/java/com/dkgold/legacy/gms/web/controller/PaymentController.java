package com.dkgold.legacy.gms.web.controller;

import com.dkgold.legacy.gms.dto.payload.CreatePaymentTrackingRequestPayload;
import com.dkgold.legacy.gms.service.PaymentTrackingService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class PaymentController {
    PaymentTrackingService paymentTrackingService;

    public PaymentController(PaymentTrackingService paymentTrackingService) {
        this.paymentTrackingService = paymentTrackingService;
    }
    @RequestMapping("/api/payment-tracking")
    public ResponseEntity createPaymentTracking(@RequestBody CreatePaymentTrackingRequestPayload paymentRequest){
        return ResponseEntity.ok(paymentTrackingService.createPaymentTracking(paymentRequest));
    }
}
