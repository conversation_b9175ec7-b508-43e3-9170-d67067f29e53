package com.dkgold.legacy.gms.service;

import com.dkgold.legacy.cocathanh.web.sale.CamVangService;
import com.dkgold.legacy.cocathanh.web.sale.SaleCartService;
import com.dkgold.legacy.gms.data.*;
import com.dkgold.legacy.gms.dto.PaymentTrackingDTO;
import com.dkgold.legacy.gms.dto.payload.CreatePaymentTrackingRequestPayload;
import com.dkgold.legacy.gms.mapper.PaymentTrackingMapper;
import com.dkgold.legacy.gms.repository.*;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.locks.ReentrantLock;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Service
public class PaymentTrackingService {


    public PaymentTrackingService(SaleCartService saleCartService,
                                  CamVangService camVangService,
                                  PaymentTrackingRepository paymentTrackingRepository,
                                  HoaDonNuTrangRepository hoaDonNuTrangRepository,
                                  BankConfigRepository bankConfigRepository,
                                  PhieuThanhToanHoadonCamRepository phieuThanhToanHoadonCamRepository,
                                  HoaDonBanKhauRepository hoaDonBanKhauRepository
    ) {
        this.saleCartService = saleCartService;
        this.camVangService = camVangService;
        this.paymentTrackingRepository = paymentTrackingRepository;
        this.hoaDonNuTrangRepository = hoaDonNuTrangRepository;
        this.bankConfigRepository = bankConfigRepository;
        this.phieuThanhToanHoadonCamRepository = phieuThanhToanHoadonCamRepository;
        this.hoaDonBanKhauRepository = hoaDonBanKhauRepository;
    }

    private SaleCartService saleCartService;
    private CamVangService camVangService;
    private PaymentTrackingRepository paymentTrackingRepository;
    private HoaDonNuTrangRepository hoaDonNuTrangRepository;
    private BankConfigRepository bankConfigRepository;
    private PhieuThanhToanHoadonCamRepository phieuThanhToanHoadonCamRepository;
    private HoaDonBanKhauRepository hoaDonBanKhauRepository;

    @Transactional
    public PaymentTrackingDTO createPaymentTracking(CreatePaymentTrackingRequestPayload payload) {
        if (payload.getIdDonHang() != null) {
            return generatePaymentTrackingForHoaDonNuTrang(payload.getIdDonHang(), payload.getAmount());
        }
        if (payload.getIdPhieuThanhToanHoaDonCam() != null) {
            return generatePaymentTrackingForPhieuThanhToanHoaDonCam(payload.getIdPhieuThanhToanHoaDonCam(), payload.getAmount());

        }
        if (payload.getIdHoaDonBanKhau() != null) {
            return generatePaymentTrackingForHoaDonBanKhau(payload.getIdHoaDonBanKhau(), payload.getAmount());
        }
        throw new IllegalArgumentException("Invalid payload");
    }

    private PaymentTrackingDTO generatePaymentTrackingForHoaDonBanKhau(Long idHoaDonBanKhau, Long amount) {
        HoaDonBanKhauEntity hoaDonBanKhauEntity = hoaDonBanKhauRepository.findById(idHoaDonBanKhau).get();
        if (hoaDonBanKhauEntity == null) {
            throw new EntityNotFoundException("Không tìm thấy hóa đơn bán khâu");
        }
        if (hoaDonBanKhauEntity.getPaymentTracking() != null) {
            if (!hoaDonBanKhauEntity.getPaymentTracking().getAmount().equals(amount)) {
                hoaDonBanKhauEntity.getPaymentTracking().setAmount(amount);
                paymentTrackingRepository.save(hoaDonBanKhauEntity.getPaymentTracking());
            }
            PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(hoaDonBanKhauEntity.getPaymentTracking());
            return dto;
        }
        String trackingCode = generateMaxTrackingCode("PTHDBK_");
        PaymentTrackingEntity paymentTrackingEntity = new PaymentTrackingEntity();
        paymentTrackingEntity.setTrackingCode(trackingCode);
        paymentTrackingEntity.setAmount(amount);
        paymentTrackingEntity.setContent(generatePymentContent(paymentTrackingEntity.getTrackingCode(), amount));
        paymentTrackingEntity.setPaymentType(PaymentTrackingEntity.TYPE_BANK_TRANSFER);
        paymentTrackingEntity.setStatus(PaymentTrackingEntity.STATUS_PENDING);
        paymentTrackingEntity.setBankConfig(getDefaultBankConfig());
        paymentTrackingRepository.save(paymentTrackingEntity);
        hoaDonBanKhauEntity.setPaymentTracking(paymentTrackingEntity);
        hoaDonBanKhauRepository.save(hoaDonBanKhauEntity);
        PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(paymentTrackingEntity);
        return dto;
    }

    private PaymentTrackingDTO generatePaymentTrackingForPhieuThanhToanHoaDonCam(Long idPhieuThanhToanHoaDonCam, Long amount) {
        PhieuThanhToanHoaDonCamEntity phieuThanhToanHoaDonCamEntity = phieuThanhToanHoadonCamRepository.findById(idPhieuThanhToanHoaDonCam).get();
        if (phieuThanhToanHoaDonCamEntity == null) {
            throw new EntityNotFoundException("Không tìm thấy phiếu thanh toán");
        }
        if (phieuThanhToanHoaDonCamEntity.getPaymentTracking() != null) {
            if (!phieuThanhToanHoaDonCamEntity.getPaymentTracking().getAmount().equals(amount)) {
                phieuThanhToanHoaDonCamEntity.getPaymentTracking().setAmount(amount);
                paymentTrackingRepository.save(phieuThanhToanHoaDonCamEntity.getPaymentTracking());
            }
            PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(phieuThanhToanHoaDonCamEntity.getPaymentTracking());
            return dto;
        }
        String trackingCode = generateMaxTrackingCode("PTHDC_");
        PaymentTrackingEntity paymentTrackingEntity = new PaymentTrackingEntity();
        paymentTrackingEntity.setTrackingCode(trackingCode);
        paymentTrackingEntity.setAmount(amount);
        paymentTrackingEntity.setContent(generatePymentContent(paymentTrackingEntity.getTrackingCode(), amount));
        paymentTrackingEntity.setPaymentType(PaymentTrackingEntity.TYPE_BANK_TRANSFER);
        paymentTrackingEntity.setStatus(PaymentTrackingEntity.STATUS_PENDING);
        paymentTrackingEntity.setBankConfig(getDefaultBankConfig());
        paymentTrackingRepository.save(paymentTrackingEntity);
        phieuThanhToanHoaDonCamEntity.setPaymentTracking(paymentTrackingEntity);
        phieuThanhToanHoadonCamRepository.save(phieuThanhToanHoaDonCamEntity);
        PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(paymentTrackingEntity);
        camVangService.getCamVangModel().getPhieuThanhToanHoaDonCam().setPaymentTracking(dto);
        return dto;
    }

    // Add a lock for concurrency control
    private final ReentrantLock lock = new ReentrantLock();

    private PaymentTrackingDTO generatePaymentTrackingForHoaDonNuTrang(Long idDonHang, Long amount) {
        try {
            // Acquire lock to prevent concurrent access
            lock.lock();
            HoaDonNuTrangEntity hoaDonNuTrangEntity = hoaDonNuTrangRepository.findById(idDonHang.intValue()).get();
            if (hoaDonNuTrangEntity == null) {
                throw new EntityNotFoundException("Không tìm thấy hóa đơn nu trang");
            }
            if (hoaDonNuTrangEntity.getPaymentTracking() != null) {
                if (!hoaDonNuTrangEntity.getPaymentTracking().getAmount().equals(amount)) {
                    hoaDonNuTrangEntity.getPaymentTracking().setAmount(amount);
                    paymentTrackingRepository.save(hoaDonNuTrangEntity.getPaymentTracking());
                }
                PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(hoaDonNuTrangEntity.getPaymentTracking());
                return dto;
            }
            PaymentTrackingEntity paymentTrackingEntity = new PaymentTrackingEntity();
            paymentTrackingEntity.setTrackingCode(generateMaxTrackingCode("PTHDNT_"));
            paymentTrackingEntity.setAmount(amount);
            paymentTrackingEntity.setContent(generatePymentContent(paymentTrackingEntity.getTrackingCode(), amount));
            paymentTrackingEntity.setPaymentType(PaymentTrackingEntity.TYPE_BANK_TRANSFER);
            paymentTrackingEntity.setStatus(PaymentTrackingEntity.STATUS_PENDING);
            paymentTrackingEntity.setBankConfig(getDefaultBankConfig());
            paymentTrackingRepository.save(paymentTrackingEntity);
            hoaDonNuTrangEntity.setPaymentTracking(paymentTrackingEntity);
            hoaDonNuTrangRepository.save(hoaDonNuTrangEntity);
            PaymentTrackingDTO dto = PaymentTrackingMapper.mapToDTO(paymentTrackingEntity);
            saleCartService.getSaleCartList().stream().filter(cart -> cart.getHoaDonMoiThanhToan() != null && cart.getHoaDonMoiThanhToan().getId().equals(idDonHang)).findFirst().ifPresent(cart -> {
                cart.setPaymentTracking(dto);
            });
            return dto;
        } finally {
            // Always release the lock
            lock.unlock();
        }
    }

    private String generatePymentContent(String trackingCode, Long amount) {
        String hashCode = generateSecurityHash(trackingCode);
        return "Ma Giao Dich: " + trackingCode + " - Ma Xac Thuc: " + hashCode;
    }

    private String generateSecurityHash(String trackingCode) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String dataToHash = trackingCode + System.getProperty("payment.secret", "defaultSecretKey");
            byte[] hashBytes = digest.digest(dataToHash.getBytes(StandardCharsets.UTF_8));
            
            // Convert to hex string (first 8 characters for brevity)
            StringBuilder hexString = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                String hex = Integer.toHexString(0xff & hashBytes[i]);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Failed to generate security hash", e);
        }
    }

    public boolean verifyTrackingCodeHash(String trackingCode, String providedHash) {
        String calculatedHash = generateSecurityHash(trackingCode);
        return calculatedHash.equals(providedHash);
    }

    private String generateMaxTrackingCode(String prefix) {
        // Generate date part in format DDMMYYYY
        SimpleDateFormat dateFormat = new SimpleDateFormat("ddMMyyyy");
        String datePart = dateFormat.format(new Date());
        // Find the highest sequence number for today
        String prefixWithDate = prefix + datePart + "_";
        String maxTrackingCode = paymentTrackingRepository.findMaxTrackingCodeByPrefix(prefixWithDate);
        int sequence = 1;
        if (maxTrackingCode != null) {
            // Extract the sequence number from the last tracking code
            String sequencePart = maxTrackingCode.substring(prefixWithDate.length());
            sequence = Integer.parseInt(sequencePart) + 1;
        }
        return prefixWithDate + sequence;
    }

    private BankConfigEntity getDefaultBankConfig() {
        return bankConfigRepository.findByIsDefault(true).get();
    }
}
