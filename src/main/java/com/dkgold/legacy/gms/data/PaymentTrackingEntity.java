package com.dkgold.legacy.gms.data;

import jakarta.persistence.*;

@Entity
@Table(name = "payment_tracking")
public class PaymentTrackingEntity {
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_SUCCESS = "success";
    public static final String STATUS_FAILED = "failed";
    public static final String STATUS_CANCEL = "cancel";

    public static final String TYPE_CASH = "cash";
    public static final String TYPE_BANK_TRANSFER = "bank_transfer";

    @Id
    @Column(name = "tracking_code")
    private String trackingCode;
    @Column(name = "payment_type", nullable = false)
    private String paymentType;
    @Column(name = "amount", nullable = false)
    private Long amount;
    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "content", nullable = false)
    private String content;

    @Column(name = "receive_amount")
    private Long receiveAmount = 0L;

    @ManyToOne
    @JoinColumn(name = "bank_config_id")
    private BankConfigEntity bankConfig;


    public String getTrackingCode() {
        return trackingCode;
    }

    public void setTrackingCode(String trackingCode) {
        this.trackingCode = trackingCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getReceiveAmount() {
        return receiveAmount;
    }

    public void setReceiveAmount(Long receiveAmount) {
        this.receiveAmount = receiveAmount;
    }

    public BankConfigEntity getBankConfig() {
        return bankConfig;
    }

    public void setBankConfig(BankConfigEntity bankConfig) {
        this.bankConfig = bankConfig;
    }
}
