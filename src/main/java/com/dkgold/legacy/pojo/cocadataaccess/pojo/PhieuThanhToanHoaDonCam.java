package com.dkgold.legacy.pojo.cocadataaccess.pojo;
import com.dkgold.legacy.gms.dto.PaymentTrackingDTO;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by THANHBEOPC on 6/12/2015.
 */
public class PhieuThanhToanHoaDonCam {
    private Long idPhieuThanhToanHoaDonCam;
    private Date ngayThanhToan;
    private Integer tongTienThanhToan;
    private Set<ChiTietThanhToanHoaDonCam> chiTietThanhToanHoaDonCams = new HashSet<>();
    private PhieuThanhToan phieuThanhToan;
    private PaymentTrackingDTO paymentTracking;

    /**
     * addition info
     * @return
     */

    private Boolean daThanhToan = false;

    public Long getIdPhieuThanhToanHoaDonCam() {
        return idPhieuThanhToanHoaDonCam;
    }

    public void setIdPhieuThanhToanHoaDonCam(Long idPhieuThanhToanHoaDonCam) {
        this.idPhieuThanhToanHoaDonCam = idPhieuThanhToanHoaDonCam;
    }

    public Date getNgayThanhToan() {
        return ngayThanhToan;
    }

    public void setNgayThanhToan(Date ngayThanhToan) {
        this.ngayThanhToan = ngayThanhToan;
    }

    public Integer getTongTienThanhToan() {
        return tongTienThanhToan;
    }

    public void setTongTienThanhToan(Integer tongTienThanhToan) {
        this.tongTienThanhToan = tongTienThanhToan;
    }

    public Set<ChiTietThanhToanHoaDonCam> getChiTietThanhToanHoaDonCams() {
        return chiTietThanhToanHoaDonCams;
    }

    public void setChiTietThanhToanHoaDonCams(Set<ChiTietThanhToanHoaDonCam> chiTietThanhToanHoaDonCams) {
        this.chiTietThanhToanHoaDonCams = chiTietThanhToanHoaDonCams;
    }
    public void updatePrice() {
        this.tongTienThanhToan = chiTietThanhToanHoaDonCams
                .stream()
                .map(item->item.getTongTien())
                .reduce(0,((last,tong)->last+tong));
    }

    public Boolean getDaThanhToan() {
        return daThanhToan;
    }

    public void setDaThanhToan(Boolean daThanhToan) {
        this.daThanhToan = daThanhToan;
    }

    public PhieuThanhToan getPhieuThanhToan() {
        return phieuThanhToan;
    }

    public void setPhieuThanhToan(PhieuThanhToan phieuThanhToan) {
        this.phieuThanhToan = phieuThanhToan;
    }

    public PaymentTrackingDTO getPaymentTracking() {
        return paymentTracking;
    }

    public void setPaymentTracking(PaymentTrackingDTO paymentTracking) {
        this.paymentTracking = paymentTracking;
    }
}
